﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    /// <summary>
    /// 系统管理
    /// </summary>
    [Auth(AuthCodes.System.Announcement.Announcement_Manage)]
    [Auth(AuthCodes.System.Announcement.Announcement_Query)]
    [ApiExplorerSettings(GroupName = "系统管理")]
    public class ManagementController : BaseApiController<ManagementBll>
    {
        #region Announcement
        /// <summary>
        /// 查询公告
        /// </summary>
        [Auth(AuthCodes.System.Announcement.Announcement_Manage)]
        [Auth(AuthCodes.System.Announcement.Announcement_Query)]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "查询公告")]
        public QueryResult<AnnouncementQuery> QueryAnnouncement([FromQuery] AnnouncementFilter filter)
        {
            var exps = this.NewExps<Announcement>();

            if (filter.Dates != null)
            {
                exps.And(p => p.StartTime <= filter.Dates[1] && p.EndTime >= filter.Dates[0]);
            }


            return this.Repo.GetDynamicQuery<Announcement, AnnouncementQuery>(filter, exps);
        }


        /// <summary>
        /// 获取公告
        /// </summary>
        [Auth(AuthCodes.System.Announcement.Announcement_Manage)]
        [Auth(AuthCodes.System.Announcement.Announcement_Query)]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取公告")]
        public BizResult<AnnouncementModel> GetAnnouncement([FromQuery, Required] Guid id)
        {
            var result = new BizResult<AnnouncementModel>();

            var entity = this.Repo.Get<Announcement>(id);

            if (entity == null)
            {
                result.Error(I18ns.Rule.Announcement.Not_Exist);
            }
            else
            {
                var model = entity.Map<AnnouncementModel>();

                result.Data = model;
            }

            return result;
        }

        /// <summary>
        /// 新增公告
        /// </summary>
        [Auth(AuthCodes.System.Announcement.Announcement_Manage)]
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "新增公告")]
        public BizResult<AnnouncementModel> AddAnnouncement(AnnouncementModel model)
        {
            var entity = model.Map<Announcement>();

            var content = model.Map<AnnouncementContent>();

            var result = this.Repo.AddAnnouncement(entity,content);

            return result.Map<AnnouncementModel>();
        }


        /// <summary>
        /// 编辑公告
        /// </summary>
        [Auth(AuthCodes.System.Announcement.Announcement_Manage)]
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "编辑公告")]
        public BizResult<AnnouncementModel> UpdateAnnouncement(AnnouncementModel model)
        {
            var entity = model.Map<Announcement>();

            var content = model.Map<AnnouncementContent>();

            var result = this.Repo.UpdateAnnouncement(entity,content);

            return result.Map<AnnouncementModel>();
        }



        /// <summary>
        /// 删除公告
        /// </summary>
        [Auth(AuthCodes.System.Announcement.Announcement_Manage)]
        [Auth(AuthCodes.System.Announcement.Announcement_Query)]
        [HttpPost]
        [LogApi(ApiType.Save, Operate = "删除公告")]
        public BizResult DelectAnnouncement(AnnouncementModel model)
        {
            var result = this.Repo.DeleteAnnouncement(model.ID);

            return result;
        }
        #endregion Announcement


        /// <summary>
        /// 分页查询客户端日志列表
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>分页客户端日志列表</returns>
        [HttpPost]
        //[Auth(AuthCodes.MasterData.Business.Shipper_Query)]
        [LogApi(ApiType.Query, Operate = "分页查询客户端日志列表")]
        public QueryResult<ReceiverClientLogQuery> QueryReceiverClientLog(ReceiverClientLogFilter filter)
        {
            return this.Repo.GetDynamicQuery<ReceiverClientLog, ReceiverClientLogQuery>(filter);
        }
    }
}
