using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Mvc;
using Shinsoft.DDI.Bll;
using Shinsoft.DDI.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Shinsoft.DDI.Api.Controllers
{
    /// <summary>
    /// 收货方管理控制器
    /// 提供收货方的增删改查等API接口
    /// </summary>
    [ApiExplorerSettings(GroupName = "收货方维护")]
    public class ReceiverController : BaseApiController<ReceiverBll>
    {

        /// <summary>
        /// 分页查询收货方列表
        /// </summary>
        /// <param name="filter">查询条件</param> 
        /// <returns>分页收货方列表</returns>
        [HttpGet]
        //[Auth(AuthCodes.MasterData.Business.Receiver_Query)]
        [LogApi(ApiType.Query, Operate = "分页查询收货方列表")]
        public QueryResult<ReceiverQuery> QueryReceiver([FromQuery] ReceiverFilter filter)
        {
            var exps = this.NewExps<Receiver>();
            
            // 选择上级单位是，排除当前ID
            if (filter.ExcludeId != null && !filter.ExcludeId.IsEmpty())
            {
                exps.And(o => o.ID != filter.ExcludeId.Value);
            }
            return this.Repo.GetDynamicQuery<Receiver, ReceiverQuery>(filter, exps);
        }

        /// <summary>
        /// 获取收货方详情
        /// </summary>
        /// <param name="id">收货方ID</param>
        /// <returns>收货方详情</returns>
        [HttpGet]
        public BizResult<ReceiverModel> Get(Guid id)
        {
            var result = new BizResult<ReceiverModel>();

            var entity = this.Repo.GetReceiver(id);
            if (entity == null)
            {
                result.Error("收货方不存在");
            }
            else
            {
                result.Data = this.Map<ReceiverModel>(entity);
            }

            return result;
        }

        /// <summary>
        /// 新增收货方
        /// </summary>
        /// <param name="model">收货方模型</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Receiver_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增收货方")]
        public BizResult<ReceiverModel> Add([FromBody] ReceiverModel model)
        {
            var entity = this.Map<Receiver>(model);
            var result = this.Repo.AddReceiver(entity);
            return result.Map<ReceiverModel>();
        }

        /// <summary>
        /// 编辑收货方
        /// </summary>
        /// <param name="model">收货方模型</param>
        /// <returns>编辑结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Receiver_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑收货方")]
        public BizResult<ReceiverModel> Edit([FromBody] ReceiverModel model)
        {
            var entity = this.Map<Receiver>(model);
            var result = this.Repo.UpdateReceiver(entity);

            return result.Map<ReceiverModel>();
        }

        /// <summary>
        /// 停用终端转换
        /// </summary>
        /// <param name="model">收货方</param>
        /// <returns>停用结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Receiver_Manage_Stop)]
        [LogApi(ApiType.Save, Operate = "停用终端转换")]
        public BizResult StopTerminal([FromBody] ConversionTerminalRequest model)
        {
            return this.Repo.StopTerminal(model.OldId, model.NewId);
        }

        /// <summary>
        /// 删除收货方
        /// </summary>
        /// <param name="model">收货方</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Receiver_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除收货方")]
        public BizResult Delete([FromBody] ReceiverModel model)
        {
            return this.Repo.DeleteReceiver(model.ID);
        }

        /// <summary>
        /// 导出收货方列表
        /// </summary>
        /// <param name="filter">查询条件</param>
        /// <returns>Excel文件字节数组</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Receiver_Manage_Export)]
        [LogApi(ApiType.Query, Operate = "导出收货方信息")]
        public FileContentResult ExportReceiver([FromBody] ReceiverFilter filter)
        {
            var entities = this.Repo.GetEntities<Receiver>(filter);
            var models = entities.Maps<ReceiverModel>();

            // byte[] result = ExcelHelper.WriteToExcelBytes(models, ReceiverDictionary.ExportReceiverColumns, "收货方信息");

            return File(new byte[0], ConstDefinition.Common.Export_ContentType, "收货方信息.xlsx");
        }
    }

}