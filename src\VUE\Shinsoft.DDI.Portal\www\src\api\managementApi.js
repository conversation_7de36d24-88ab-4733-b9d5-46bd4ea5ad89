import axios from '@/utils/axios'

/**
 * 货主相关API
 */
export const managementApi = {
  /**
   * 查询客户端日志列表 (POST请求)
   * @param {Object} data 查询参数
   * @param {number} data.pageIndex 页码
   * @param {number} data.pageSize 页大小
   * @param {string} data.receiverCode 客户编码
   * @param {string} data.receiverName 客户名称
   * @param {string} data.message 日志内容
   * @param {Array<string>} data.logTimeRange 日志时间范围
   * @param {string} data.order 排序
   * @returns {Promise} API响应
   */
  queryReceiverClientLog(data) {
    return axios.post('/Management/QueryReceiverClientLog', data)
  },

}

export default managementApi