using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace Shinsoft.DDI.Bll
{
    /// <summary>
    /// 货主业务逻辑层
    /// 提供货主实体的增删改查等业务操作
    /// </summary>
    public class ShipperBll : BaseBll
    {
        #region Constructs

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUser">操作用户</param>
        public ShipperBll(IUser? operatorUser = null)
            : base(operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operatorUniqueName">操作用户唯一名称</param>
        public ShipperBll(string operatorUniqueName)
            : base(operatorUniqueName) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serviceProvider">服务提供者</param>
        /// <param name="operatorUser">操作用户</param>
        public ShipperBll(IServiceProvider serviceProvider, IUser? operatorUser = null)
            : base(serviceProvider, operatorUser) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="bll">业务逻辑层接口</param>
        public ShipperBll(IRepo bll)
            : base(bll) { }

        #endregion Constructs

        #region Shipper 货主管理

        /// <summary>
        /// 添加货主
        /// </summary>
        /// <param name="entity">货主实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Shipper> AddShipper(Shipper entity)
        {
            var result = new BizResult<Shipper>();

            // 验证必填字段
            if (entity.Name.IsEmpty())
            {
                result.Error("请输入货主名称");
            }
            else
            {
                // 检查名称是否重复
                var existByName = this.GetEntity<Shipper>(p => p.Name == entity.Name);
                if (existByName != null)
                {
                    result.Error("已存在相同名称的货主");
                }
            }

            if (result.Success)
            {
                if (entity.ShortName.IsEmpty())
                {
                    result.Error("请输入货主简称");
                }
                else
                {
                    // 检查简称是否重复
                    var existByName = this.GetEntity<Shipper>(p => p.ShortName == entity.ShortName);
                    if (existByName != null)
                    {
                        result.Error("已存在相同简称的货主");
                    }
                }
            }

            if (result.Success)
            {
                // 生成ID
                if (entity.ID.IsEmpty())
                {
                    entity.ID = CombGuid.NewGuid();
                }

                // 自动生成编码
                entity.Code = GetSerialNumber(ConstDefinition.CodePrefix.Shipper_CodePrefix);

                entity.EnumStatus = ShipperStatus.Valid;

                // 处理默认货主逻辑
                if (entity.IsDefault)
                {
                    // 如果设置为默认货主，需要将其他货主的默认标志设为false
                    var existingDefaultShippers = this.GetEntities<Shipper>(p => p.IsDefault == true);
                    foreach (var existingShipper in existingDefaultShippers)
                    {
                        existingShipper.IsDefault = false;
                        this.Update(existingShipper);
                    }
                }

                entity = this.Add(entity);

                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 更新货主
        /// </summary>
        /// <param name="entity">货主实体</param>
        /// <returns>操作结果</returns>
        public BizResult<Shipper> UpdateShipper(Shipper entity)
        {
            var result = new BizResult<Shipper>();

            var dbEntity = this.Get<Shipper>(entity.ID);
            if (dbEntity == null)
            {
                result.Error("货主不存在");
            }
            else
            {
                // 验证必填字段
                if (entity.Name.IsEmpty())
                {
                    result.Error("请输入货主名称");
                }
                else
                {
                    // 检查名称是否重复
                    var existByName = this.GetEntity<Shipper>(p => p.Name == entity.Name && p.ID != entity.ID);
                    if (existByName != null)
                    {
                        result.Error("已存在相同名称的货主");
                    }
                }
            }

            if (result.Success)
            {
                if (entity.ShortName.IsEmpty())
                {
                    result.Error("请输入货主简称");
                }
                else
                {
                    // 检查简称是否重复
                    var existByName = this.GetEntity<Shipper>(p => p.ShortName == entity.ShortName && p.ID != entity.ID);
                    if (existByName != null)
                    {
                        result.Error("已存在相同简称的货主");
                    }
                }
            }

            if (result.Success)
            {
                // 处理默认货主逻辑
                if (entity.IsDefault)
                {
                    // 如果设置为默认货主，需要将其他货主的默认标志设为false
                    var existingDefaultShippers = this.GetEntities<Shipper>(p => p.IsDefault == true && p.ID != entity.ID);
                    foreach (var existingShipper in existingDefaultShippers)
                    {
                        existingShipper.IsDefault = false;
                        this.Update(existingShipper);
                    }
                }

                this.Update(entity);

                result.Data = entity;
            }

            return result;
        }

        /// <summary>
        /// 停用货主
        /// </summary>
        /// <param name="id">货主ID</param>
        /// <returns>操作结果</returns>
        public BizResult StopShipper(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Shipper>(id);
            if (dbEntity == null)
            {
                result.Error("货主不存在");
            }
            else
            {
                dbEntity.EnumStatus = ShipperStatus.Stoped;
                this.Update(dbEntity);
            }

            return result;
        }

        /// <summary>
        /// 启用货主
        /// </summary>
        /// <param name="id">货主ID</param>
        /// <returns>操作结果</returns>
        public BizResult EnableShipper(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Shipper>(id);
            if (dbEntity == null)
            {
                result.Error("货主不存在");
            }
            else
            {
                dbEntity.EnumStatus = ShipperStatus.Valid;
                this.Update(dbEntity);
            }

            return result;
        }

        /// <summary>
        /// 删除货主
        /// </summary>
        /// <param name="id">货主ID</param>
        /// <returns>操作结果</returns>
        public BizResult DeleteShipper(Guid id)
        {
            var result = new BizResult();

            var dbEntity = this.Get<Shipper>(id);
            if (dbEntity == null)
            {
                result.Error("货主不存在");
            }
            else
            {
                if (dbEntity.ShipperProductSpec.Count > 0)
                {
                    result.Error("货主已关联正在使用品规,不能删除");
                }
                else if (dbEntity.ShipperReceiver.Count > 0)
                {
                    result.Error("货主已关联正在使用经销商,不能删除");
                }
                else
                {
                    this.Delete(dbEntity);
                }
            }

            return result;
        }

        /// <summary>
        /// 获取货主详情
        /// </summary>
        /// <param name="id">货主ID</param>
        /// <returns>货主实体</returns>
        public Shipper? GetShipper(Guid id)
        {
            return this.GetEntity<Shipper>(p => p.ID == id);
        }

        #endregion Shipper 货主管理
    }


}