using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Mvc;
using Shinsoft.DDI.Bll;
using Shinsoft.DDI.Common;
using Shinsoft.DDI.Api.Models.SelectorModels;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Shinsoft.DDI.Api.Controllers
{
    /// <summary>
    /// 产品控制器
    /// 提供产品相关的API接口，包括分页查询、获取详情、新增、编辑、删除等功能
    /// </summary>
    [ApiExplorerSettings(GroupName = "产品维护")]
    public class ProductController : BaseApiController<ProductBll>
    {
        #region 产品管理

        /// <summary>
        /// 分页查询产品列表
        /// </summary>
        /// <param name="filter">查询条件</param> 
        /// <returns>分页产品列表</returns>
        [HttpGet]
        //[Auth(AuthCodes.MasterData.Business.Product_Query)]
        [LogApi(ApiType.Query, Operate = "分页查询产品列表")]
        public QueryResult<ProductQuery> QueryProduct([FromQuery] ProductFilter filter)
        {
            return this.Repo.GetDynamicQuery<Product, ProductQuery>(filter);
        }

        /// <summary>
        /// 获取产品详情
        /// </summary>
        /// <param name="id">产品ID</param>
        /// <returns>产品详情</returns>
        [HttpGet]
        public BizResult<ProductModel> Get(Guid id)
        {
            var result = new BizResult<ProductModel>();

            var entity = this.Repo.Get<Product>(id);
            if (entity == null)
            {
                result.Error("产品不存在");
            }
            else
            {
                result.Data = this.Map<ProductModel>(entity);
            }

            return result;
        }

        /// <summary>
        /// 新增产品
        /// </summary>
        /// <param name="model">产品模型</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Product_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增产品")]
        public BizResult<ProductModel> Add([FromBody] ProductModel model)
        {
            var entity = this.Map<Product>(model);
            var result = this.Repo.AddProduct(entity);
            return result.Map<ProductModel>();
        }

        /// <summary>
        /// 编辑产品
        /// </summary>
        /// <param name="model">产品模型</param>
        /// <returns>编辑结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Product_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑产品")]
        public BizResult<ProductModel> Edit([FromBody] ProductModel model)
        {
            var entity = this.Map<Product>(model);
            var result = this.Repo.UpdateProduct(entity);

            return result.Map<ProductModel>();
        }

        /// <summary>
        /// 删除产品
        /// </summary>
        /// <param name="model">产品</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Product_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除产品")]
        public BizResult Delete([FromBody] ProductModel model)
        {
            return this.Repo.DeleteProduct(model.ID);
        }

        /// <summary>
        /// 获取产品级联选择器数据（药企\产品\规格）
        /// </summary>
        /// <returns>级联选择器数据</returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取药品、产品、规格级联选择器数据")]
        public BizResult<List<ProductCascaderSelector>> GetProductSpecCascader()
        {
            var result = new BizResult<List<ProductCascaderSelector>>();

            try
            {
                // 获取所有启用的药企
                var manufacturers = this.Repo.GetEntities<Manufacturer>(m => m.EnumStatus == ManufacturerStatus.Enable)
                    .OrderBy(m => m.Name)
                    .ToList();

                var cascaderNodes = new List<ProductCascaderSelector>();

                foreach (var manufacturer in manufacturers)
                {
                    // 获取该药企下的所有产品
                    var products = this.Repo.GetEntities<Product>(p => p.ManufacturerId == manufacturer.ID)
                        .OrderBy(p => p.NameCn)
                        .ToList();

                    var productNodes = new List<ProductCascaderSelector>();

                    foreach (var product in products)
                    {
                        // 获取该产品下的所有规格
                        var specs = this.Repo.GetEntities<ProductSpec>(s => s.ProductId == product.ID)
                            .OrderBy(s => s.Spec)
                            .ToList();

                        var specNodes = specs.Select(spec => new ProductCascaderSelector
                        {
                            Value = spec.ID.ToString(),
                            Label = spec.Spec,
                        }).ToList();

                        productNodes.Add(new ProductCascaderSelector
                        {
                            Value = product.ID.ToString(),
                            Label = product.NameCn,
                            Children = specNodes
                        });
                    }

                    cascaderNodes.Add(new ProductCascaderSelector
                    {
                        Value = manufacturer.ID.ToString(),
                        Label = manufacturer.Name,
                        Children = productNodes
                    });
                }

                result.Data = cascaderNodes;
            }
            catch (Exception ex)
            {
                result.Error($"获取产品级联选择器数据失败：{ex.Message}");
            }

            return result;
        }

        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取药品、产品级联选择器数据")]
        public BizResult<List<ProductCascaderSelector>> GetProductCascader()
        {
            var result = new BizResult<List<ProductCascaderSelector>>();

            try
            {
                // 获取所有启用的药企
                var manufacturers = this.Repo.GetEntities<Manufacturer>(m => m.EnumStatus == ManufacturerStatus.Enable)
                    .OrderBy(m => m.Name)
                    .ToList();

                var cascaderNodes = new List<ProductCascaderSelector>();

                foreach (var manufacturer in manufacturers)
                {
                    // 获取该药企下的所有产品
                    var products = this.Repo.GetEntities<Product>(p => p.ManufacturerId == manufacturer.ID)
                        .OrderBy(p => p.NameCn)
                        .ToList();
 
                    cascaderNodes.Add(new ProductCascaderSelector
                    {
                        Value = manufacturer.ID.ToString(),
                        Label = manufacturer.Name,
                        Children = products.Select(p => new ProductCascaderSelector
                        {
                            Value = p.ID.ToString(),
                            Label = p.NameCn
                        }).ToList()
                    });
                }

                result.Data = cascaderNodes;
            }
            catch (Exception ex)
            {
                result.Error($"获取产品级联选择器数据失败：{ex.Message}");
            }

            return result;
        }

        #endregion 产品管理
    }
}