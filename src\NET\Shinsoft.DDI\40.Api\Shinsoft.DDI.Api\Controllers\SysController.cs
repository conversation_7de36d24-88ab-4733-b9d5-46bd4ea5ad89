﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shinsoft.DDI.Api
{
    [ApiExplorerSettings(GroupName = "")]
    public class SysController : BaseApiController<SysBll>, ILoginController
    {
        public string LoginName { get; set; } = string.Empty;

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取Guid")]
        public Guid NewGuid()
        {
            return CombGuid.NewGuid();
        }

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取Guid列表")]
        public List<Guid> NewGuids([FromQuery] int count = 10)
        {
            var list = new List<Guid>();

            for (var i = 1; i <= count; i++)
            {
                list.Add(CombGuid.NewGuid());
            }

            return list;
        }

        /// <summary>
        /// 登录
        /// </summary>
        [AllowAnonymous]
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.Login)]
        public BizResult<IdentityUser> UserLogin([FromBody] LoginRequest request)
        {
            return this.UserProvider.UserLogin(request.LoginName, request.Password);
        }

        /// <summary>
        /// 刷新令牌
        /// </summary>
        [AllowAnonymous]
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.Login, Operate = "刷新令牌")]
        public BizResult<IdentityUser> RefreshToken([FromBody] TokenLoginRequest request)
        {
            var result = new BizResult<IdentityUser>();

            if (request.Token.IsEmpty())
            {
                result.Info("令牌不可以为空");
            }
            else
            {
                try
                {
                    var json = AesHelper.Decrypt(request.Token, Config.UserAuthorize.RefreshToken.AesKey);

                    var identityExpiry = json.DeserializeJson<IdentityExpiry>();

                    if (identityExpiry.IdentityKey.IsEmpty())
                    {
                        result.Info("令牌无效");
                    }
                    else if (identityExpiry.Expiry < DateTime.Now)
                    {
                        result.Info("令牌超时");
                    }
                    else
                    {
                        var loginResult = this.UserProvider.UserLogin(identityExpiry.IdentityKey);

                        if (loginResult.Success)
                        {
                            result.Data = loginResult.Data;
                        }
                        else
                        {
                            foreach (var msg in loginResult.Messages)
                            {
                                result.Info(msg);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Info(ex.Message);
                }
            }

            if (result.Success && result.Data != null)
            {
                this.LoginName = result.Data.LoginName;
            }

            return result;
        }

        /// <summary>
        /// 自动登录
        /// </summary>
        [AllowAnonymous]
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.Login, Operate = "自动登录")]
        public BizResult<IdentityUser> AutoLogin([FromBody] TokenLoginRequest request)
        {
            var result = new BizResult<IdentityUser>();

            if (request.Token.IsEmpty())
            {
                result.Info("令牌不可以为空");
            }
            else
            {
                try
                {
                    var json = AesHelper.Decrypt(request.Token, Config.UserAuthorize.AutoLogin.AesKey);

                    var identityExpiry = json.DeserializeJson<IdentityExpiry>();

                    if (identityExpiry.IdentityKey.IsEmpty())
                    {
                        result.Info("令牌无效");
                    }
                    else if (identityExpiry.Expiry < DateTime.Now)
                    {
                        result.Info("令牌超时");
                    }
                    else
                    {
                        var loginResult = this.UserProvider.UserLogin(identityExpiry.IdentityKey);

                        if (loginResult.Success)
                        {
                            result.Data = loginResult.Data;
                        }
                        else
                        {
                            foreach (var msg in loginResult.Messages)
                            {
                                result.Info(msg);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Info(ex.Message);
                }
            }

            if (result.Success && result.Data != null)
            {
                this.LoginName = result.Data.LoginName;
            }

            return result;
        }

        /// <summary>
        /// SSO登录
        /// </summary>
        [AllowAnonymous]
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.Login, Operate = "SSO登录")]
        public BizResult<IdentityUser> SsoLogin([FromBody] TokenLoginRequest request)
        {
            var result = new BizResult<IdentityUser>();

            if (request.Token.IsEmpty())
            {
                result.Error("令牌不可以为空");
            }
            else
            {
                var json = AesHelper.Decrypt(request.Token, Config.SsoSite.AesKey);

                var ssoToken = json.DeserializeJson<SsoToken>();

                if (ssoToken.LoginName.IsEmpty())
                {
                    result.Error("令牌无效");
                }
                else if (ssoToken.Expiry < DateTime.Now)
                {
                    result.Info("令牌超时");
                }
                else
                {
                    result = this.UserProvider.UserLogin(ssoToken.LoginName);
                }
            }

            if (result.Success)
            {
                this.LoginName = result.Data!.LoginName;
            }

            return result;
        }

        /// <summary>
        /// 登出
        /// </summary>
        [HttpGet]
        [JwtIgnore]
        [LogApi(ApiType.Logout)]
        public BizResult UserLogout()
        {
            if (this.IsUserAuthenticated)
            {
                this.UserProvider.UserLogout(this.User.GetIdentityJson());
            }

            return this.BizResult();
        }

        /// <summary>
        /// 切换语言
        /// </summary>
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.SwitchCulture, Operate = "切换语言")]
        public BizResult<IdentityUser> SwitchCulture([FromBody] SwitchCultureRequest request)
        {
            return this.UserProvider.SwitchCulture(request.Culture);
        }

        /// <summary>
        /// 切换个人身份
        /// </summary>
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.SwitchMyIdentity, Operate = "切换个人身份")]
        public BizResult<IdentityUser> SwitchMyIdentity([FromBody] SwitchIdentityRequest request)
        {
            return this.UserProvider.SwitchMyIdentity(request.NewIdentityId);
        }

        /// <summary>
        /// 切换代理身份
        /// </summary>
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.SwitchToAgent, Operate = "切换代理身份")]
        public BizResult<IdentityUser> SwitchToAgent([FromBody] SwitchIdentityRequest request)
        {
            return this.UserProvider.SwitchToAgent(request.NewIdentityId);
        }

        /// <summary>
        /// 切换货主
        /// </summary>
        [JwtIgnore]
        [HttpPost]
        [LogApi(ApiType.SwitchMyIdentity, Operate = "切换货主")]
        public BizResult<IdentityUser> SwitchShipper([FromBody] SwitchIdentityRequest request)
        {
            //todo： request改一下
            return this.UserProvider.SwitchShipper(request.NewIdentityId);
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取当前用户信息")]
        public BizResult<IdentityUser> GetCurrentUser()
        {
            var result = new BizResult<IdentityUser> { Data = this.UserProvider.GetIdentityUser(this.User.GetIdentityJson()) };

            return result;
        }

        [AllowAnonymous]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "AES加密")]
        public string AesEncrypt([FromQuery, Required] string str)
        {
            return AesHelper.Encrypt(str, Config.Secret.AesKey);
        }

        [AllowAnonymous]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "AES解密")]
        public string AesDecrypt([FromQuery, Required] string str)
        {
            return AesHelper.Decrypt(str, Config.Secret.AesKey);
        }

        [AllowAnonymous]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "DES加密")]
        public string DesEncrypt([FromQuery, Required] string str)
        {
            return DesHelper.Encrypt(str, Config.Secret.DesKey);
        }

        [AllowAnonymous]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "DES解密")]
        public string DesDecrypt([FromQuery, Required] string str)
        {
            return DesHelper.Decrypt(str, Config.Secret.DesKey);
        }

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取站点网址")]
        public string GetSiteUri()
        {
            return this.Request.GetSiteUri();
        }

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取应用网址")]
        public BizResult<string> GetApiUri()
        {
            return new BizResult<string>(Config.SitePath.ApiUri);
        }

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取手机版网址")]
        public string GetMobileUri()
        {
            return Config.SitePath.MobileUri;
        }

        [AllowAnonymous]
        [JwtIgnore]
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取PC版网址")]
        public string GetPcUri()
        {
            return Config.SitePath.PcUri;
        }
    }
}