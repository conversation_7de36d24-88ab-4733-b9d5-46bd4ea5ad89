using Microsoft.AspNetCore.Mvc;
using Shinsoft.Core.Mvc;
using Shinsoft.DDI.Bll;
using Shinsoft.DDI.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Shinsoft.DDI.Api.Controllers
{
    /// <summary>
    /// 药企管理控制器
    /// 提供药企的增删改查等API接口
    /// </summary>
    [ApiExplorerSettings(GroupName = "药企维护")]
    public class ManufacturerController : BaseApiController<ManufacturerBll>
    {
        /// <summary>
        /// 分页查询药企列表
        /// </summary>
        /// <param name="filter">查询条件</param> 
        /// <returns>分页药企列表</returns>
        [HttpGet]
        //[Auth(AuthCodes.MasterData.Business.Manufacturer_Query)]
        [LogApi(ApiType.Query, Operate = "分页查询药企列表")]
        public QueryResult<ManufacturerQuery> QueryManufacturer([FromQuery] ManufacturerFilter filter)
        {
            return this.Repo.GetDynamicQuery<Manufacturer, ManufacturerQuery>(filter);
        }

        /// <summary>
        /// 获取药企详情
        /// </summary>
        /// <param name="id">药企ID</param>
        /// <returns>药企详情</returns>
        [HttpGet]
        public BizResult<ManufacturerModel> Get(Guid id)
        {
            var result = new BizResult<ManufacturerModel>();

            var entity = this.Repo.GetManufacturer(id);
            if (entity == null)
            {
                result.Error("指定的药企不存在，请联系管理员。");
            }
            else
            {
                result.Data = this.Map<ManufacturerModel>(entity);
            }

            return result;
        }

        /// <summary>
        /// 新增药企
        /// </summary>
        /// <param name="model">药企模型</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Manufacturer_Manage_Add)]
        [LogApi(ApiType.Save, Operate = "新增药企")]
        public BizResult<ManufacturerModel> Add([FromBody] ManufacturerModel model)
        {
            var entity = this.Map<Manufacturer>(model);
            var result = this.Repo.AddManufacturer(entity);
            return result.Map<ManufacturerModel>();
        }

        /// <summary>
        /// 编辑药企
        /// </summary>
        /// <param name="model">药企模型</param>
        /// <returns>编辑结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Manufacturer_Manage_Edit)]
        [LogApi(ApiType.Save, Operate = "编辑药企")]
        public BizResult<ManufacturerModel> Edit([FromBody] ManufacturerModel model)
        {
            var entity = this.Map<Manufacturer>(model);
            var result = this.Repo.UpdateManufacturer(entity);

            return result.Map<ManufacturerModel>();
        }

        /// <summary>
        /// 删除药企
        /// </summary>
        /// <param name="model">药企</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        // [Auth(AuthCodes.MasterData.Business.Manufacturer_Manage_Delete)]
        [LogApi(ApiType.Save, Operate = "删除药企")]
        public BizResult Delete([FromBody] ManufacturerModel model)
        {
            return this.Repo.DeleteManufacturer(model.ID);
        }

        /// <summary>
        /// 获取药企选择器列表（不翻页）
        /// </summary>
        /// <returns>药企选择器列表</returns>
        [HttpGet]
        [LogApi(ApiType.Query, Operate = "获取药企选择器列表")]
        public BizResult<List<ManufacturerSelector>> GetManufacturerSelector()
        {
            var result = new BizResult<List<ManufacturerSelector>>();

            try
            {
                // 获取所有启用状态的药企，按名称排序
                var manufacturers = this.Repo.GetEntities<Manufacturer>(m => m.EnumStatus == ManufacturerStatus.Enable)
                    .OrderBy(m => m.Name)
                    .ToList();

                // 映射为选择器格式
                var selectorList = manufacturers.Select(m => new ManufacturerSelector
                {
                    ID = m.ID,
                    Code = m.Code,
                    Name = m.Name,
                    ShortName = m.ShortName
                }).ToList();

                result.Data = selectorList;
            }
            catch (Exception ex)
            {
                result.Error($"获取药企选择器列表失败：{ex.Message}");
            }

            return result;
        }
    }
}