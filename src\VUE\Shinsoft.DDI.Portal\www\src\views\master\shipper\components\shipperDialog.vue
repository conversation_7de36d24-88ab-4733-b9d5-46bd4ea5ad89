<!--
/**
 * 货主管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑货主' : '新增货主'"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="shipperFormRef"
      :model="shipperForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <!-- 货主编码 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="货主编码" prop="code">
            <el-input v-model="shipperForm.code" placeholder="请输入货主编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="isEdit ? 12 : 24">
          <el-form-item label="货主名称" prop="name">
            <el-input v-model="shipperForm.name" placeholder="请输入货主名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="简称" prop="shortName">
            <el-input v-model="shipperForm.shortName" placeholder="请输入简称" />
          </el-form-item>
        </el-col>
        <!-- 状态 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="状态" prop="enumStatus">
            <el-select v-model="shipperForm.enumStatus" placeholder="请选择状态" style="width: 100%">
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.text"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 是否默认货主 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="是否默认货主" prop="isDefault">
            <el-switch
              v-model="shipperForm.isDefault"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 联系信息 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input v-model="shipperForm.address" placeholder="请输入地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="shipperForm.contactPerson" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="telephone">
            <el-input v-model="shipperForm.telephone" placeholder="请输入电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="邮箱" prop="eMail">
            <el-input v-model="shipperForm.eMail" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" >
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, getCurrentInstance, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { shipperApi } from '@/api/shipperApi'
import { selectorApi } from '@/api/selectorApi'

export default {
  name: 'shipperDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 获取当前实例以访问全局属性
    const instance = getCurrentInstance()
    // 状态选项数据
    const statusOptions = ref([])
    
    // 表单引用
    const shipperFormRef = ref(null)
 
    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)
    
    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })
    
    // 表单数据
    const shipperForm = reactive({
      id: undefined,
      code: '',
      name: '',
      shortName: '',
      address: '',
      telephone: '',
      eMail: '',
      contactPerson: '',
      enumStatus: 1,
      isDefault: false
    })

    // 表单验证规则 - 动态规则，根据编辑模式调整
    const formRules = computed(() => ({
      code: isEdit.value ? [
        { required: true, message: '请输入货主编码', trigger: ['blur', 'change'] },
        { max: 50, message: '编码长度不能超过50个字符', trigger: ['blur', 'change'] }
      ] : [],
      name: [
        { required: true, message: '请输入货主名称', trigger: ['blur', 'change'] },
        { max: 200, message: '名称长度不能超过200个字符', trigger: ['blur', 'change'] }
      ],
      shortName: [
        { required: true, message: '请输入简称', trigger: ['blur', 'change'] },
        { max: 50, message: '简称长度不能超过50个字符', trigger: ['blur', 'change'] }
      ],
      enumStatus: isEdit.value ? [
        { required: true, message: '请选择状态', trigger: ['blur', 'change'] }
      ] : [],
      address: [
        { max: 500, message: '地址长度不能超过500个字符', trigger: ['blur', 'change'] }
      ],
      telephone: [
        { max: 50, message: '电话长度不能超过50个字符', trigger: ['blur', 'change'] }
      ],
      eMail: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] },
        { max: 50, message: '邮箱长度不能超过50个字符', trigger: ['blur', 'change'] }
      ],
      contactPerson: [
        { max: 50, message: '联系人长度不能超过50个字符', trigger: ['blur', 'change'] }
      ]
    }))

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(shipperForm, {
        id: undefined,
        code: '',
        name: '',
        shortName: '',
        address: '',
        telephone: '',
        eMail: '',
        contactPerson: '',
        enumStatus: 1,
        isDefault: false
      })

      if (shipperFormRef.value) {
        // 先重置字段，再延迟清除验证状态
        shipperFormRef.value.resetFields()
        setTimeout(() => {
          if (shipperFormRef.value) {
            shipperFormRef.value.clearValidate()
          }
        }, 100)
      }
    }

    const initStatusOptions = async () => {
      try {
        const response = await selectorApi.getEnumInfos({ enumType: 'ShipperStatus' })

        if (response.data && response.data.success !== false) {
          const data = response.data.data || response.data
          statusOptions.value = data.map(item => ({
            value: item.value,
            text: item.text
          }))
        } else {
          console.error('获取状态选项失败:', response.data?.message)
          statusOptions.value = []
        }
      } catch (error) {
        console.error('获取状态选项失败:', error)
        statusOptions.value = []
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = (recordId) => {
      shipperApi.getShipper(recordId).then(response => {
        if (response.data.success) {
          Object.assign(shipperForm, response.data.data);
        } else {
          ElMessage.error(response.message || '加载数据失败');
        }
      });     
    }

    /**
     * 保存货主信息
     */
    const handleSave = () => {     
      shipperFormRef.value.validate((valid) => {
        if (valid) {
  
          const apiMethod = isEdit.value ? shipperApi.editShipper : shipperApi.addShipper;
    
          apiMethod(shipperForm).then(response => {
            
             if (response.data.success) {
               ElMessage.success(isEdit.value ? '货主信息更新成功' : '货主添加成功');
               emit('success');
               handleClose();
             } else {
               ElMessage.error(response.message || '操作失败');
             }
          
           }).catch(error => {
             ElMessage.error('操作失败：' + error.message);
              
           });
 
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        // 使用nextTick确保DOM已更新
        nextTick(async () => {
          // 延迟清除验证状态，确保表单完全渲染
          setTimeout(() => {
            if (shipperFormRef.value) {
              shipperFormRef.value.clearValidate()
            }
          }, 50)
          
          if (isEdit.value && props.recordId) {
            await initStatusOptions()
            // 编辑模式，加载数据
            loadRecordData(props.recordId)
          } else {
            // 新增模式，重置表单
            resetForm()
          }
        })
      }
    })

    return {
      shipperFormRef,
      isEdit,
      dialogVisible,
      statusOptions,
      shipperForm,
      formRules,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>
