import axios from 'axios'
import router from '../router/index'
import { ElLoading, ElMessage } from 'element-plus'
// import store from '../store/index'

// 清除用户数据的工具函数
function clearUserData() {
    // 清除token和用户信息
    localStorage.removeItem('token');
    localStorage.removeItem('tokenStorage');
    localStorage.removeItem('userId');
    localStorage.removeItem('account');
    localStorage.removeItem('name');
    localStorage.removeItem('userInfo');

    // 清除其他可能的用户相关数据
    sessionStorage.clear();
}

// axios 配置  timeout单位为毫秒
var timeOut = 1000 * 60 * 30
axios.defaults.timeout = timeOut
axios.defaults.baseURL = import.meta.env.VITE_API_HOST
axios.defaults.headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*'
}

// axios.defaults.transformRequest = [function (data) {
//   data = Qs.stringify(data)
//   return data
// }]
// axios.defaults.withCredentials = true

// http request 拦截器
var num = 0
let loadingInstance = null
axios.interceptors.request.use(
    config => {
        num++
        if (sessionStorage.demoSpinIconLoad === 'true') {
            loadingInstance = ElLoading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0, 0, 0, 0.7)',
            })
            sessionStorage.demoSpinIconLoad = 'true'
        } else {
            sessionStorage.demoSpinIconLoad = 'true'
        }
        if (localStorage.token) {
            config.headers.Authorization = 'Bearer ' + localStorage.token
            config.headers['Accept-Language'] = localStorage.getItem('currentLanguage') === null ? 'zh-CN' : localStorage.getItem('currentLanguage') === 'mergeZH' ? 'zh-CN' : 'en-US'
            config.headers['route'] = sessionStorage.route
        }

        // 处理GET请求的数组参数序列化
        if (config.method === 'get' && config.params) {
            config.paramsSerializer = function(params) {
                const searchParams = new URLSearchParams();
                for (const key in params) {
                    if (params.hasOwnProperty(key)) {
                        const value = params[key];
                        if (Array.isArray(value)) {
                            // 对于数组，使用重复的键名格式：key=value1&key=value2
                            value.forEach(item => {
                                if (item !== null && item !== undefined && item !== '') {
                                    searchParams.append(key, item);
                                }
                            });
                        } else if (value !== null && value !== undefined && value !== '') {
                            searchParams.append(key, value);
                        }
                    }
                }
                const queryString = searchParams.toString();
                console.log('序列化后的查询参数:', queryString);
                return queryString;
            };
        }

        config.data = JSON.stringify(config.data)
        return config
    },
    err => {
        return Promise.reject(err)
    }
)
// http response 拦截器
axios.interceptors.response.use(
    response => {
        num--
        if (num <= 0 && loadingInstance) {
            loadingInstance.close()
            loadingInstance = null
        }
        if (response.status === 204) {
            return response
        } else {
            // 处理嵌套的data结构，类似Demo项目的处理方式
            const result = response.data;

            // 如果返回的数据结构是 { data: { datas: [...], total: 100 } }
            // 则将其扁平化为 { datas: [...], total: 100 }
            if (result && result.hasOwnProperty("data") && result.data?.hasOwnProperty("datas")) {
                Object.keys(result.data).forEach(prop => {
                    result[prop] = result.data[prop];
                });
                delete result.data;
            }

            return response
        }
    },
    error => {
        num--
        if (num <= 0 && loadingInstance) {
            loadingInstance.close()
            loadingInstance = null
        }
        if (error.response) {
            switch (error.response.status) {
            case 500:
                if (error.response.data) {
                    let duration = 5
                    if (error.response.config.responseType === 'arraybuffer') {
                        let unit8Arr = new Uint8Array(error.response.data)
                        let encodedString = String.fromCharCode.apply(null, unit8Arr)
                        let decodedString = decodeURIComponent(escape((encodedString)))
                        ElMessage.error({
                            message: JSON.parse(JSON.parse(decodedString)).Message,
                            duration: 5000
                        })
                    } else {
                        if (JSON.parse(error.response.data).Message.length > 20) {
                            duration = 8
                        }
                        ElMessage.error({
                            message: JSON.parse(error.response.data).Message,
                            duration: duration * 1000
                        })
                    }
                } else {
                    ElMessage.error({
                        message: '请求服务器被拒绝，请清空浏览器缓存后重试',
                        duration: 3000
                    })
                }
                break
            case 400:
                if (error.response.data) {
                    let duration = 5
                    if (error.response.config.responseType === 'arraybuffer') {
                        let unit8Arr = new Uint8Array(error.response.data)
                        let encodedString = String.fromCharCode.apply(null, unit8Arr)
                        let decodedString = decodeURIComponent(escape((encodedString)))
                        ElMessage.error({
                            message: JSON.parse(JSON.parse(decodedString)).Message,
                            duration: 5000
                        })
                    } else {
                        if (JSON.parse(error.response.data).Message.length > 20) {
                            duration = 8
                        }
                        ElMessage.info({
                            message: JSON.parse(error.response.data).Message,
                            duration: duration * 1000
                        })
                    }
                } else {
                    ElMessage.error({
                        message: '当前操作引发未知错误，请联系管理员',
                        duration: 3000
                    })
                }
                break
            case 401:
                if (error.response.data === '用户身份信息已过期。') {
                    // 清除用户数据
                    clearUserData()
                    router.push({ name: 'loginJump', params: { code: 'system.userIdentityExpired' } })
                } else {
                    if (error.response.data) {
                        ElMessage.error({
                            message: '没有权限访问',
                            duration: 3000
                        })
                    } else {
                        ElMessage.error({
                            message: error.response.data,
                            duration: 3000
                        })
                    }

                    router.push({ path: '/403' })
                }
                break
            case 404:
                ElMessage.error({
                    message: '找不到访问的资源',
                    duration: 3000
                })
                break
            case 405:
                ElMessage.error({
                    message: '访问失败，请联系管理员',
                    duration: 5000
                })
                break
            default:
                ElMessage.error({
                    message: '未知错误',
                    duration: 3000
                })
                break
            }
        } else {
            ElMessage.error({
                message: '未知错误',
                duration: 3000
            })
        }
        return Promise.reject(error)
    }
)
export default axios
